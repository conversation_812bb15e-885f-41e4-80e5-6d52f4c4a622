/* AI配置修复测试页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn.primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.btn.secondary {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.btn.tertiary {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.btn.quaternary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn.fifth {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: white;
}

.btn[disabled] {
  background: #cccccc !important;
  color: #999999 !important;
}

.result-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.status.success {
  background: #e8f5e8;
  color: #4caf50;
}

.status.error {
  background: #ffeaea;
  color: #f44336;
}

.fixes {
  margin-bottom: 20rpx;
}

.fixes-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}

.fix-item {
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
  border-left: 4rpx solid #4facfe;
}

.fix-message {
  font-size: 26rpx;
  color: #333;
  display: block;
}

.fix-action {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-top: 5rpx;
  font-style: italic;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.status-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.status-value {
  font-size: 26rpx;
  font-weight: bold;
}

.status-value.ready {
  color: #4caf50;
}

.status-value.not-ready {
  color: #f44336;
}

.logs-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.logs-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.logs-actions {
  display: flex;
  gap: 10rpx;
}

.btn-small {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
  color: #666;
  border: none;
}

.logs-container {
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
}

.log-item {
  margin-bottom: 10rpx;
  padding: 10rpx;
  background: white;
  border-radius: 8rpx;
  border-left: 3rpx solid #4facfe;
}

.log-text {
  font-size: 24rpx;
  color: #333;
  font-family: monospace;
}

.log-empty {
  text-align: center;
  padding: 60rpx;
  color: #999;
  font-size: 26rpx;
}

.help-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.help-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.help-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
