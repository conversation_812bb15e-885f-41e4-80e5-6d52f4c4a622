/**
 * AI配置诊断云函数
 * 检查AI配置状态和连接性
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🔍 开始AI配置诊断...');
  
  const diagnosis = {
    timestamp: new Date().toISOString(),
    checks: [],
    summary: '',
    recommendations: []
  };

  try {
    // 1. 检查ai_configs集合
    console.log('📋 检查ai_configs集合...');
    const aiConfigsResult = await db.collection('ai_configs').get();
    
    diagnosis.checks.push({
      name: 'ai_configs集合检查',
      status: 'success',
      details: {
        totalConfigs: aiConfigsResult.data.length,
        configs: aiConfigsResult.data.map(config => ({
          id: config._id,
          name: config.name,
          provider: config.provider,
          model: config.model,
          status: config.status,
          hasApiKey: !!(config.config?.apiKey),
          apiKeyLength: config.config?.apiKey?.length || 0,
          baseURL: config.config?.baseURL,
          isDefault: config.isDefault || false
        }))
      }
    });

    // 2. 检查激活的配置
    console.log('🎯 检查激活的AI配置...');
    const activeConfigResult = await db.collection('ai_configs')
      .where({
        status: 'active'
      })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get();

    if (activeConfigResult.data.length > 0) {
      const activeConfig = activeConfigResult.data[0];
      diagnosis.checks.push({
        name: '激活配置检查',
        status: 'success',
        details: {
          configId: activeConfig._id,
          name: activeConfig.name,
          provider: activeConfig.provider,
          model: activeConfig.model,
          hasApiKey: !!(activeConfig.config?.apiKey),
          apiKeyPreview: activeConfig.config?.apiKey ? 
            activeConfig.config.apiKey.substring(0, 8) + '...' : 'null',
          baseURL: activeConfig.config?.baseURL,
          maxTokens: activeConfig.config?.maxTokens,
          temperature: activeConfig.config?.temperature
        }
      });

      // 3. 测试API连接（如果有API密钥）
      if (activeConfig.config?.apiKey) {
        console.log('🌐 测试豆包AI连接...');
        try {
          const testResult = await testDoubaoConnection(activeConfig);
          diagnosis.checks.push({
            name: 'API连接测试',
            status: testResult.success ? 'success' : 'error',
            details: testResult
          });
        } catch (testError) {
          diagnosis.checks.push({
            name: 'API连接测试',
            status: 'error',
            details: {
              error: testError.message,
              stack: testError.stack
            }
          });
        }
      } else {
        diagnosis.checks.push({
          name: 'API连接测试',
          status: 'skipped',
          details: {
            reason: 'API密钥为空，跳过连接测试'
          }
        });
      }
    } else {
      diagnosis.checks.push({
        name: '激活配置检查',
        status: 'error',
        details: {
          error: '没有找到激活的AI配置'
        }
      });
    }

    // 4. 检查system_config集合（备用配置）
    console.log('🔧 检查system_config集合...');
    const systemConfigResult = await db.collection('system_config')
      .where({
        type: 'ai_config',
        status: 'active'
      })
      .get();

    diagnosis.checks.push({
      name: 'system_config备用配置检查',
      status: 'info',
      details: {
        hasBackupConfig: systemConfigResult.data.length > 0,
        backupConfigs: systemConfigResult.data.map(config => ({
          id: config._id,
          model: config.model,
          provider: config.provider,
          hasApiKey: !!(config.apiKey)
        }))
      }
    });

    // 5. 检查环境变量
    console.log('🌍 检查环境变量...');
    diagnosis.checks.push({
      name: '环境变量检查',
      status: 'info',
      details: {
        hasDoubaoApiKey: !!(process.env.DOUBAO_API_KEY),
        hasDoubaoApiUrl: !!(process.env.DOUBAO_API_URL),
        hasDoubaoModel: !!(process.env.DOUBAO_MODEL),
        apiKeyLength: process.env.DOUBAO_API_KEY?.length || 0,
        apiUrl: process.env.DOUBAO_API_URL || 'not set',
        model: process.env.DOUBAO_MODEL || 'not set'
      }
    });

    // 生成诊断总结和建议
    generateSummaryAndRecommendations(diagnosis);

    return {
      success: true,
      diagnosis: diagnosis
    };

  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error);
    diagnosis.checks.push({
      name: '诊断过程',
      status: 'error',
      details: {
        error: error.message,
        stack: error.stack
      }
    });

    return {
      success: false,
      diagnosis: diagnosis,
      error: error.message
    };
  }
};

/**
 * 测试豆包AI连接
 */
async function testDoubaoConnection(config) {
  const https = require('https');
  const { URL } = require('url');

  return new Promise((resolve) => {
    try {
      const apiUrl = config.config.baseURL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
      const url = new URL(apiUrl);

      const requestData = {
        model: config.model,
        messages: [
          {
            role: 'user',
            content: '测试连接'
          }
        ],
        temperature: 0.7,
        max_tokens: 10,
        stream: false
      };

      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.config.apiKey}`
        },
        timeout: 10000
      };

      const req = https.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve({
            success: res.statusCode === 200,
            statusCode: res.statusCode,
            responseLength: data.length,
            responsePreview: data.substring(0, 200),
            headers: res.headers
          });
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message,
          errorType: 'request_error'
        });
      });

      req.on('timeout', () => {
        resolve({
          success: false,
          error: '请求超时',
          errorType: 'timeout'
        });
      });

      req.write(JSON.stringify(requestData));
      req.end();

    } catch (error) {
      resolve({
        success: false,
        error: error.message,
        errorType: 'setup_error'
      });
    }
  });
}

/**
 * 生成诊断总结和建议
 */
function generateSummaryAndRecommendations(diagnosis) {
  const errors = diagnosis.checks.filter(check => check.status === 'error');
  const successes = diagnosis.checks.filter(check => check.status === 'success');

  if (errors.length === 0) {
    diagnosis.summary = '✅ AI配置检查通过，所有组件正常';
  } else {
    diagnosis.summary = `❌ 发现 ${errors.length} 个问题需要解决`;
  }

  // 生成具体建议
  const recommendations = [];

  // 检查是否有激活的配置
  const activeConfigCheck = diagnosis.checks.find(check => check.name === '激活配置检查');
  if (activeConfigCheck?.status === 'error') {
    recommendations.push({
      priority: 'high',
      action: '激活AI配置',
      description: '需要在管理后台激活一个AI模型配置',
      steps: [
        '1. 进入管理后台的AI模型管理页面',
        '2. 配置豆包AI的API密钥',
        '3. 将模型状态设置为"激活"'
      ]
    });
  }

  // 检查API密钥
  const apiTestCheck = diagnosis.checks.find(check => check.name === 'API连接测试');
  if (apiTestCheck?.status === 'error') {
    recommendations.push({
      priority: 'high',
      action: '修复API连接',
      description: 'API连接测试失败，请检查API密钥和网络',
      steps: [
        '1. 验证豆包AI的API密钥是否正确',
        '2. 检查API URL是否正确',
        '3. 确认模型ID是否有效',
        '4. 检查网络连接'
      ]
    });
  }

  diagnosis.recommendations = recommendations;
}
