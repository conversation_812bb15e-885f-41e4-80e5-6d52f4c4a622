/**
 * 检查和修复豆包AI API密钥的云函数
 * 专门解决HTTP 401认证失败问题
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🔑 开始检查豆包AI API密钥...');
  
  const result = {
    timestamp: new Date().toISOString(),
    checks: [],
    issues: [],
    recommendations: []
  };

  try {
    // 1. 检查激活的AI配置
    console.log('📋 检查激活的AI配置...');
    const activeConfigResult = await db.collection('ai_configs')
      .where({
        status: 'active'
      })
      .get();

    if (activeConfigResult.data.length === 0) {
      result.issues.push({
        type: 'no_active_config',
        severity: 'high',
        message: '没有找到激活的AI配置',
        solution: '需要在管理后台激活一个AI模型配置'
      });
      
      result.checks.push({
        name: '激活配置检查',
        status: 'failed',
        details: '没有激活的AI配置'
      });
    } else if (activeConfigResult.data.length > 1) {
      result.issues.push({
        type: 'multiple_active_configs',
        severity: 'medium',
        message: `发现${activeConfigResult.data.length}个激活的AI配置`,
        solution: '应该只保留一个激活配置'
      });
    } else {
      const activeConfig = activeConfigResult.data[0];
      
      result.checks.push({
        name: '激活配置检查',
        status: 'success',
        details: {
          configId: activeConfig._id,
          name: activeConfig.name,
          model: activeConfig.model,
          provider: activeConfig.provider
        }
      });

      // 2. 检查API密钥
      console.log('🔑 检查API密钥...');
      const apiKey = activeConfig.config?.apiKey;
      
      if (!apiKey) {
        result.issues.push({
          type: 'missing_api_key',
          severity: 'high',
          message: '激活的配置缺少API密钥',
          solution: '需要在管理后台配置豆包AI的API密钥'
        });
        
        result.checks.push({
          name: 'API密钥检查',
          status: 'failed',
          details: 'API密钥为空'
        });
      } else {
        // 检查API密钥格式
        const keyInfo = analyzeAPIKey(apiKey);
        
        result.checks.push({
          name: 'API密钥检查',
          status: keyInfo.valid ? 'success' : 'warning',
          details: {
            length: keyInfo.length,
            format: keyInfo.format,
            preview: keyInfo.preview,
            valid: keyInfo.valid,
            issues: keyInfo.issues
          }
        });

        if (!keyInfo.valid) {
          result.issues.push({
            type: 'invalid_api_key_format',
            severity: 'high',
            message: `API密钥格式可能有问题: ${keyInfo.issues.join(', ')}`,
            solution: '请检查API密钥是否正确复制，确保没有多余的空格或字符'
          });
        }

        // 3. 测试API密钥有效性
        if (keyInfo.valid) {
          console.log('🌐 测试API密钥有效性...');
          const testResult = await testAPIKey(activeConfig);
          
          result.checks.push({
            name: 'API密钥有效性测试',
            status: testResult.success ? 'success' : 'failed',
            details: testResult
          });

          if (!testResult.success) {
            let severity = 'high';
            let solution = '请检查API密钥是否正确';

            if (testResult.statusCode === 401) {
              solution = 'API密钥无效或已过期，请重新获取有效的豆包AI API密钥';
            } else if (testResult.statusCode === 403) {
              solution = 'API密钥权限不足，请检查密钥是否有调用权限';
            } else if (testResult.statusCode === 429) {
              severity = 'medium';
              solution = 'API调用频率超限，请稍后重试';
            } else if (testResult.error && testResult.error.includes('timeout')) {
              severity = 'medium';
              solution = '网络连接超时，请检查网络连接';
            }

            result.issues.push({
              type: 'api_key_test_failed',
              severity: severity,
              message: `API密钥测试失败: ${testResult.error}`,
              solution: solution,
              statusCode: testResult.statusCode
            });
          }
        }
      }

      // 4. 检查其他配置
      console.log('⚙️ 检查其他配置...');
      const configIssues = checkOtherConfigs(activeConfig);
      
      result.checks.push({
        name: '其他配置检查',
        status: configIssues.length > 0 ? 'warning' : 'success',
        details: {
          model: activeConfig.model,
          baseURL: activeConfig.config?.baseURL,
          issues: configIssues
        }
      });

      result.issues.push(...configIssues);
    }

    // 5. 检查环境变量备用配置
    console.log('🌍 检查环境变量备用配置...');
    const envCheck = checkEnvironmentVariables();
    
    result.checks.push({
      name: '环境变量检查',
      status: 'info',
      details: envCheck
    });

    // 6. 生成建议
    generateRecommendations(result);

    return {
      success: true,
      result: result
    };

  } catch (error) {
    console.error('❌ 检查过程失败:', error);
    return {
      success: false,
      error: error.message,
      result: result
    };
  }
};

/**
 * 分析API密钥格式
 */
function analyzeAPIKey(apiKey) {
  const info = {
    length: apiKey.length,
    preview: apiKey.substring(0, 8) + '...' + apiKey.substring(apiKey.length - 4),
    valid: true,
    issues: [],
    format: 'unknown'
  };

  // 检查长度
  if (apiKey.length < 20) {
    info.valid = false;
    info.issues.push('密钥长度过短');
  }

  // 检查是否包含空格
  if (apiKey.includes(' ')) {
    info.valid = false;
    info.issues.push('包含空格字符');
  }

  // 检查是否包含换行符
  if (apiKey.includes('\n') || apiKey.includes('\r')) {
    info.valid = false;
    info.issues.push('包含换行符');
  }

  // 检查豆包AI密钥格式
  if (apiKey.startsWith('sk-')) {
    info.format = 'openai_like';
  } else if (apiKey.length === 64 && /^[a-f0-9]+$/i.test(apiKey)) {
    info.format = 'hex_64';
  } else if (apiKey.length > 50 && /^[A-Za-z0-9+/=]+$/.test(apiKey)) {
    info.format = 'base64_like';
  } else {
    info.format = 'custom';
  }

  return info;
}

/**
 * 测试API密钥有效性
 */
async function testAPIKey(config) {
  const https = require('https');
  const { URL } = require('url');

  return new Promise((resolve) => {
    try {
      const apiUrl = config.config?.baseURL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
      const url = new URL(apiUrl);

      const requestData = {
        model: config.model,
        messages: [
          {
            role: 'user',
            content: '测试'
          }
        ],
        temperature: 0.7,
        max_tokens: 5,
        stream: false
      };

      const postData = JSON.stringify(requestData);
      
      const options = {
        hostname: url.hostname,
        port: 443,
        path: url.pathname,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.config.apiKey}`,
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        },
        timeout: 15000
      };

      const req = https.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve({
            success: res.statusCode === 200,
            statusCode: res.statusCode,
            responseLength: data.length,
            error: res.statusCode !== 200 ? `HTTP ${res.statusCode}` : null,
            responsePreview: data.substring(0, 100)
          });
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message,
          errorType: 'network_error'
        });
      });

      req.on('timeout', () => {
        resolve({
          success: false,
          error: '请求超时',
          errorType: 'timeout'
        });
      });

      req.write(postData);
      req.end();

    } catch (error) {
      resolve({
        success: false,
        error: error.message,
        errorType: 'setup_error'
      });
    }
  });
}

/**
 * 检查其他配置项
 */
function checkOtherConfigs(config) {
  const issues = [];

  // 检查模型ID
  if (!config.model) {
    issues.push({
      type: 'missing_model',
      severity: 'high',
      message: '缺少模型ID配置',
      solution: '需要配置有效的豆包AI模型ID'
    });
  }

  // 检查API URL
  const baseURL = config.config?.baseURL;
  if (!baseURL) {
    issues.push({
      type: 'missing_base_url',
      severity: 'medium',
      message: '缺少API基础URL配置',
      solution: '建议配置豆包AI的API基础URL'
    });
  } else if (!baseURL.startsWith('https://')) {
    issues.push({
      type: 'insecure_url',
      severity: 'medium',
      message: 'API URL不是HTTPS协议',
      solution: '建议使用HTTPS协议的API URL'
    });
  }

  return issues;
}

/**
 * 检查环境变量
 */
function checkEnvironmentVariables() {
  return {
    hasDoubaoApiKey: !!(process.env.DOUBAO_API_KEY),
    hasDoubaoApiUrl: !!(process.env.DOUBAO_API_URL),
    hasDoubaoModel: !!(process.env.DOUBAO_MODEL),
    apiKeyLength: process.env.DOUBAO_API_KEY?.length || 0
  };
}

/**
 * 生成修复建议
 */
function generateRecommendations(result) {
  const highIssues = result.issues.filter(issue => issue.severity === 'high');
  const mediumIssues = result.issues.filter(issue => issue.severity === 'medium');

  if (highIssues.length > 0) {
    result.recommendations.push({
      priority: 'urgent',
      title: '立即修复高优先级问题',
      actions: highIssues.map(issue => issue.solution)
    });
  }

  if (mediumIssues.length > 0) {
    result.recommendations.push({
      priority: 'medium',
      title: '建议修复中等优先级问题',
      actions: mediumIssues.map(issue => issue.solution)
    });
  }

  // 通用建议
  result.recommendations.push({
    priority: 'general',
    title: '通用建议',
    actions: [
      '确保豆包AI API密钥是最新的有效密钥',
      '定期检查API密钥的使用额度和权限',
      '在管理后台测试AI连接功能',
      '查看云函数日志获取详细错误信息'
    ]
  });
}
