/**
 * 快速修复AI配置云函数
 * 自动检查并修复常见的AI配置问题
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🔧 开始快速修复AI配置...');
  
  const fixes = [];
  let success = false;

  try {
    // 1. 检查是否有激活的AI配置
    console.log('📋 检查激活的AI配置...');
    const activeConfigResult = await db.collection('ai_configs')
      .where({
        status: 'active'
      })
      .get();

    if (activeConfigResult.data.length === 0) {
      console.log('❌ 没有激活的AI配置，尝试激活默认配置...');
      
      // 查找默认配置
      const defaultConfigResult = await db.collection('ai_configs')
        .where({
          isDefault: true
        })
        .limit(1)
        .get();

      if (defaultConfigResult.data.length > 0) {
        const defaultConfig = defaultConfigResult.data[0];
        
        // 激活默认配置
        await db.collection('ai_configs')
          .doc(defaultConfig._id)
          .update({
            data: {
              status: 'active',
              updateTime: db.serverDate(),
              updateTimestamp: Date.now()
            }
          });

        fixes.push({
          type: 'activate_default',
          message: `已激活默认AI配置: ${defaultConfig.name}`,
          configId: defaultConfig._id
        });

        console.log(`✅ 已激活默认配置: ${defaultConfig.name}`);
      } else {
        // 创建默认配置
        console.log('📝 创建默认AI配置...');
        
        const newConfigResult = await db.collection('ai_configs').add({
          data: {
            name: '豆包AI（自动创建）',
            provider: 'doubao',
            model: 'ep-20241201193454-8xqzx',
            config: {
              apiKey: process.env.DOUBAO_API_KEY || '',
              baseURL: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
              maxTokens: 2000,
              temperature: 0.7
            },
            status: 'active',
            inputPrice: 0.001,
            outputPrice: 0.002,
            usage: 0,
            totalCost: 0,
            createTime: db.serverDate(),
            updateTime: db.serverDate(),
            createTimestamp: Date.now(),
            updateTimestamp: Date.now(),
            isDefault: true,
            description: '自动创建的默认豆包AI配置'
          }
        });

        fixes.push({
          type: 'create_default',
          message: '已创建并激活默认AI配置',
          configId: newConfigResult._id
        });

        console.log('✅ 已创建默认配置');
      }
    } else {
      console.log('✅ 找到激活的AI配置');
      
      // 检查API密钥
      const activeConfig = activeConfigResult.data[0];
      if (!activeConfig.config?.apiKey) {
        console.log('⚠️ 激活的配置缺少API密钥');
        
        if (process.env.DOUBAO_API_KEY) {
          // 使用环境变量的API密钥
          await db.collection('ai_configs')
            .doc(activeConfig._id)
            .update({
              data: {
                'config.apiKey': process.env.DOUBAO_API_KEY,
                updateTime: db.serverDate(),
                updateTimestamp: Date.now()
              }
            });

          fixes.push({
            type: 'update_api_key',
            message: '已从环境变量更新API密钥',
            configId: activeConfig._id
          });

          console.log('✅ 已更新API密钥');
        } else {
          fixes.push({
            type: 'missing_api_key',
            message: '警告：激活的配置缺少API密钥，需要手动配置',
            configId: activeConfig._id,
            action: '请在管理后台配置豆包AI的API密钥'
          });
        }
      }
    }

    // 2. 检查提示词模板
    console.log('📝 检查提示词模板...');
    const templateResult = await db.collection('prompt_templates')
      .where({
        enabled: true
      })
      .get();

    if (templateResult.data.length === 0) {
      console.log('📝 创建默认提示词模板...');
      
      const defaultTemplates = [
        {
          type: 'gentle',
          name: '温和亲切型',
          template: '你是一位温和亲切的老师，请根据学生{{studentName}}的表现生成一段温暖鼓励的评语。\n\n学生表现：\n{{performanceMaterial}}\n\n请生成100-150字的评语。',
          enabled: true,
          version: 1,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        },
        {
          type: 'formal',
          name: '正式规范型',
          template: '你是一位严谨的教师，请根据学生{{studentName}}的表现生成一段正式规范的评语。\n\n学生表现：\n{{performanceMaterial}}\n\n请生成100-150字的评语。',
          enabled: true,
          version: 1,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      ];

      for (const template of defaultTemplates) {
        await db.collection('prompt_templates').add({
          data: template
        });
      }

      fixes.push({
        type: 'create_templates',
        message: '已创建默认提示词模板',
        count: defaultTemplates.length
      });

      console.log('✅ 已创建默认提示词模板');
    }

    // 3. 测试AI连接（如果有API密钥）
    const finalConfigCheck = await db.collection('ai_configs')
      .where({
        status: 'active'
      })
      .limit(1)
      .get();

    if (finalConfigCheck.data.length > 0 && finalConfigCheck.data[0].config?.apiKey) {
      console.log('🌐 测试AI连接...');
      
      try {
        const testResult = await testAIConnection(finalConfigCheck.data[0]);
        if (testResult.success) {
          fixes.push({
            type: 'connection_test',
            message: 'AI连接测试成功',
            details: testResult
          });
          success = true;
        } else {
          fixes.push({
            type: 'connection_failed',
            message: 'AI连接测试失败',
            error: testResult.error,
            action: '请检查API密钥和网络连接'
          });
        }
      } catch (testError) {
        fixes.push({
          type: 'connection_error',
          message: '连接测试出错',
          error: testError.message
        });
      }
    }

    return {
      success: true,
      message: '快速修复完成',
      fixes: fixes,
      aiReady: success,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ 快速修复失败:', error);
    return {
      success: false,
      error: error.message,
      fixes: fixes
    };
  }
};

/**
 * 测试AI连接
 */
async function testAIConnection(config) {
  const https = require('https');
  const { URL } = require('url');

  return new Promise((resolve) => {
    try {
      const apiUrl = config.config.baseURL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
      const url = new URL(apiUrl);

      const requestData = {
        model: config.model,
        messages: [
          {
            role: 'user',
            content: '测试'
          }
        ],
        temperature: 0.7,
        max_tokens: 10,
        stream: false
      };

      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.config.apiKey}`
        },
        timeout: 10000
      };

      const req = https.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve({
            success: res.statusCode === 200,
            statusCode: res.statusCode,
            hasResponse: data.length > 0
          });
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message
        });
      });

      req.on('timeout', () => {
        resolve({
          success: false,
          error: '连接超时'
        });
      });

      req.write(JSON.stringify(requestData));
      req.end();

    } catch (error) {
      resolve({
        success: false,
        error: error.message
      });
    }
  });
}
