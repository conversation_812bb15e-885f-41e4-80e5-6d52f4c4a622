/**
 * 简单的AI配置问题诊断
 */

console.log('🔍 AI配置问题诊断\n');

console.log('根据你的错误信息"🎯 AI生成失败: Error: 豆包AI调用失败: 响应解析失败"，');
console.log('问题很可能出现在以下几个方面：\n');

console.log('📋 可能的问题原因：');
console.log('1. ❌ AI配置未激活');
console.log('   - 数据库中没有 status: "active" 的AI配置');
console.log('   - 云函数无法找到有效的AI模型配置');

console.log('\n2. ❌ API密钥问题');
console.log('   - API密钥为空或无效');
console.log('   - API密钥格式不正确');

console.log('\n3. ❌ 模型ID错误');
console.log('   - 配置的模型ID不存在或已过期');
console.log('   - 豆包AI返回错误响应');

console.log('\n4. ❌ 网络或API问题');
console.log('   - 豆包AI服务暂时不可用');
console.log('   - 返回的响应格式不是标准JSON');

console.log('\n💡 解决步骤：');

console.log('\n第一步：检查AI配置状态');
console.log('1. 进入小程序管理后台');
console.log('2. 找到"AI模型管理"或"系统设置"');
console.log('3. 检查是否有激活的AI模型配置');
console.log('4. 确保状态为"激活"或"active"');

console.log('\n第二步：配置豆包AI密钥');
console.log('1. 获取有效的豆包AI API密钥');
console.log('2. 在管理后台配置API密钥');
console.log('3. 确保模型ID正确（如：ep-20241201193454-8xqzx）');
console.log('4. 保存并激活配置');

console.log('\n第三步：测试配置');
console.log('1. 在管理后台测试AI连接');
console.log('2. 或者重新尝试生成评语');
console.log('3. 查看云函数日志确认问题');

console.log('\n🚨 紧急解决方案：');
console.log('如果管理后台无法访问，可以：');
console.log('1. 检查云函数环境变量');
console.log('2. 在云函数中直接配置API密钥');
console.log('3. 使用云开发控制台查看数据库');

console.log('\n📞 需要帮助？');
console.log('请提供以下信息：');
console.log('1. 管理后台的AI配置截图');
console.log('2. 云函数日志的详细错误信息');
console.log('3. 数据库ai_configs集合的数据');

console.log('\n🎯 最快解决方法：');
console.log('1. 确保有一个status为"active"的AI配置');
console.log('2. 确保该配置有有效的API密钥');
console.log('3. 重新部署callDoubaoAPI云函数');

console.log('\n✅ 完成诊断');
