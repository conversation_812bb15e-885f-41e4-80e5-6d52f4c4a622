/**
 * 调试豆包AI响应的云函数
 * 专门用于捕获和分析豆包AI的实际响应格式
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🔍 开始调试豆包AI响应...');
  
  try {
    // 获取激活的AI配置
    const configResult = await db.collection('ai_configs')
      .where({
        status: 'active'
      })
      .limit(1)
      .get();

    if (!configResult.data || configResult.data.length === 0) {
      return {
        success: false,
        error: '没有找到激活的AI配置'
      };
    }

    const dbConfig = configResult.data[0];
    const aiConfig = {
      model: dbConfig.model,
      apiUrl: dbConfig.config?.baseURL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      apiKey: dbConfig.config?.api<PERSON>ey,
      provider: dbConfig.name
    };

    if (!aiConfig.apiKey) {
      return {
        success: false,
        error: '激活的AI配置缺少API密钥'
      };
    }

    console.log('🎯 使用配置:', {
      name: dbConfig.name,
      model: aiConfig.model,
      apiUrl: aiConfig.apiUrl,
      hasApiKey: !!aiConfig.apiKey
    });

    // 调用豆包AI进行测试
    const testResult = await testDoubaoAPI(aiConfig);
    
    return {
      success: true,
      config: {
        name: dbConfig.name,
        model: aiConfig.model,
        apiUrl: aiConfig.apiUrl,
        hasApiKey: !!aiConfig.apiKey
      },
      testResult: testResult
    };

  } catch (error) {
    console.error('❌ 调试过程失败:', error);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    };
  }
};

/**
 * 测试豆包AI API调用
 */
async function testDoubaoAPI(config) {
  const https = require('https');
  const { URL } = require('url');

  return new Promise((resolve) => {
    try {
      const requestData = {
        model: config.model,
        messages: [
          {
            role: 'user',
            content: '请生成一段简短的学生评语测试，内容关于张三同学学习认真。'
          }
        ],
        temperature: 0.7,
        max_tokens: 100,
        stream: false
      };

      const postData = JSON.stringify(requestData);
      const parsedUrl = new URL(config.apiUrl);
      
      console.log('🚀 发送请求到:', config.apiUrl);
      console.log('📝 请求数据:', JSON.stringify(requestData, null, 2));
      
      const options = {
        hostname: parsedUrl.hostname,
        port: 443,
        path: parsedUrl.pathname,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        },
        timeout: 30000
      };

      const req = https.request(options, (res) => {
        let data = '';
        
        console.log('📥 响应状态码:', res.statusCode);
        console.log('📥 响应头:', JSON.stringify(res.headers, null, 2));
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          console.log('📥 原始响应数据长度:', data.length);
          console.log('📥 原始响应数据:', data);
          
          const result = {
            success: res.statusCode === 200,
            statusCode: res.statusCode,
            headers: res.headers,
            rawData: data,
            dataLength: data.length,
            timestamp: new Date().toISOString()
          };

          // 尝试解析响应
          if (res.statusCode === 200) {
            try {
              // 清理数据
              let cleanedData = data.trim();
              
              // 移除BOM
              if (cleanedData.charCodeAt(0) === 0xFEFF) {
                cleanedData = cleanedData.slice(1);
                result.bomRemoved = true;
              }
              
              // 移除控制字符
              const originalLength = cleanedData.length;
              cleanedData = cleanedData.replace(/[\x00-\x1F\x7F]/g, '');
              if (cleanedData.length !== originalLength) {
                result.controlCharsRemoved = originalLength - cleanedData.length;
              }
              
              result.cleanedData = cleanedData;
              result.cleanedLength = cleanedData.length;
              
              // 尝试解析JSON
              const parsed = JSON.parse(cleanedData);
              result.parsed = parsed;
              result.parseSuccess = true;
              
              // 检查结构
              if (parsed.choices && Array.isArray(parsed.choices) && parsed.choices.length > 0) {
                const choice = parsed.choices[0];
                if (choice.message && choice.message.content) {
                  result.contentExtracted = choice.message.content;
                  result.contentLength = choice.message.content.length;
                  result.structureValid = true;
                } else {
                  result.structureValid = false;
                  result.structureError = 'choice.message.content不存在';
                }
              } else {
                result.structureValid = false;
                result.structureError = 'choices数组不存在或为空';
              }
              
            } catch (parseError) {
              result.parseSuccess = false;
              result.parseError = parseError.message;
              
              // 尝试查找JSON片段
              const jsonStart = data.indexOf('{');
              const jsonEnd = data.lastIndexOf('}');
              
              if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
                const jsonFragment = data.substring(jsonStart, jsonEnd + 1);
                result.jsonFragment = jsonFragment;
                
                try {
                  const fragmentParsed = JSON.parse(jsonFragment);
                  result.fragmentParsed = fragmentParsed;
                  result.fragmentParseSuccess = true;
                } catch (fragmentError) {
                  result.fragmentParseSuccess = false;
                  result.fragmentError = fragmentError.message;
                }
              }
            }
          } else {
            result.error = `HTTP ${res.statusCode} 错误`;
            
            // 尝试解析错误响应
            try {
              const errorData = JSON.parse(data);
              result.errorData = errorData;
            } catch (e) {
              result.errorData = data;
            }
          }
          
          resolve(result);
        });
      });

      req.on('error', (error) => {
        console.error('❌ HTTPS请求失败:', error);
        resolve({
          success: false,
          error: error.message,
          errorType: 'request_error'
        });
      });

      req.on('timeout', () => {
        console.error('❌ 请求超时');
        resolve({
          success: false,
          error: '请求超时',
          errorType: 'timeout'
        });
      });

      req.write(postData);
      req.end();

    } catch (error) {
      resolve({
        success: false,
        error: error.message,
        errorType: 'setup_error'
      });
    }
  });
}
