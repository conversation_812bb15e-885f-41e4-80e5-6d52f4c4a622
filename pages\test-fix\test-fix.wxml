<!--AI配置修复测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🔧 AI配置修复工具</text>
    <text class="subtitle">自动检查并修复AI配置问题</text>
  </view>

  <view class="actions">
    <button
      class="btn primary"
      bindtap="fixAIStatus"
      disabled="{{fixing}}"
    >
      {{fixing ? '修复中...' : '修复AI状态'}}
    </button>

    <button
      class="btn secondary"
      bindtap="startFix"
      disabled="{{fixing}}"
    >
      {{fixing ? '修复中...' : '完整修复'}}
    </button>

    <button
      class="btn tertiary"
      bindtap="checkAPIKey"
      disabled="{{fixing}}"
    >
      检查API密钥
    </button>

    <button
      class="btn quaternary"
      bindtap="debugDoubaoAPI"
      disabled="{{fixing}}"
    >
      调试豆包AI
    </button>

    <button
      class="btn fifth"
      bindtap="testGenerate"
      disabled="{{fixing || !result || !result.aiReady}}"
    >
      测试生成评语
    </button>
  </view>

  <view class="result" wx:if="{{result}}">
    <view class="result-header">
      <text class="result-title">修复结果</text>
      <text class="result-status {{result.success ? 'success' : 'error'}}">
        {{result.success ? '✅ 成功' : '❌ 失败'}}
      </text>
    </view>

    <view class="fixes" wx:if="{{result.fixes && result.fixes.length > 0}}">
      <text class="fixes-title">执行的修复操作：</text>
      <view class="fix-item" wx:for="{{result.fixes}}" wx:key="index">
        <text class="fix-message">{{item.message}}</text>
        <text class="fix-action" wx:if="{{item.action}}">{{item.action}}</text>
      </view>
    </view>

    <view class="ai-status">
      <text class="status-label">AI状态：</text>
      <text class="status-value {{result.aiReady ? 'ready' : 'not-ready'}}">
        {{result.aiReady ? '🟢 就绪' : '🔴 未就绪'}}
      </text>
    </view>
  </view>

  <view class="logs" wx:if="{{logs.length > 0}}">
    <view class="logs-header">
      <text class="logs-title">修复日志</text>
      <view class="logs-actions">
        <button class="btn-small" bindtap="copyLogs">复制</button>
        <button class="btn-small" bindtap="clearLogs">清空</button>
      </view>
    </view>

    <scroll-view class="logs-content" scroll-y>
      <view class="log-item" wx:for="{{logs}}" wx:key="index">
        <text class="log-text">{{item}}</text>
      </view>
    </scroll-view>
  </view>

  <view class="tips">
    <text class="tips-title">💡 使用说明</text>
    <text class="tips-text">1. 点击"开始修复"自动检查并修复AI配置问题</text>
    <text class="tips-text">2. 修复完成后可以点击"测试生成评语"验证</text>
    <text class="tips-text">3. 如果仍有问题，请查看修复日志获取详细信息</text>
  </view>
</view>
