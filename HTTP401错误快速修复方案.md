# HTTP 401 错误快速修复方案

## 🔍 问题诊断

你的系统存在**配置架构混乱**问题：

- `callDoubaoAPI` 云函数从 `ai_configs` 集合读取配置
- 管理后台可能在使用 `system_config` 集合
- 两套系统不同步，导致云函数读不到API密钥

## 🚀 快速修复方案

### 方案一：在小程序开发者工具中直接修复

1. **打开小程序开发者工具**
2. **在控制台中运行以下代码**：

```javascript
// 检查当前配置状态
wx.cloud.callFunction({
  name: 'debugAIConfig',
  data: {}
}).then(res => {
  console.log('配置状态:', JSON.stringify(res.result, null, 2));
});
```

3. **如果发现配置问题，运行修复代码**：

```javascript
// 使用你的真实API密钥替换 'your-real-api-key'
wx.cloud.callFunction({
  name: 'quickFixAIConfig',
  data: {
    action: 'auto',
    apiKey: 'your-real-api-key'  // 🔑 替换为你的真实豆包AI密钥
  }
}).then(res => {
  console.log('修复结果:', JSON.stringify(res.result, null, 2));
});
```

### 方案二：手动数据库修复

1. **进入云开发控制台 → 数据库**

2. **检查 `ai_configs` 集合**：
   - 查看是否有 `status: 'active'` 的记录
   - 检查该记录的 `config.apiKey` 是否有值

3. **如果没有激活配置，手动添加**：

```json
{
  "name": "豆包AI",
  "provider": "doubao",
  "model": "doubao-pro-4k",
  "status": "active",
  "config": {
    "apiKey": "你的真实API密钥",
    "baseURL": "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
    "maxTokens": 2000,
    "temperature": 0.7
  },
  "inputPrice": 0.001,
  "outputPrice": 0.002,
  "usage": 0,
  "totalCost": 0,
  "createTime": "2024-01-01T00:00:00.000Z",
  "updateTime": "2024-01-01T00:00:00.000Z",
  "isDefault": true
}
```

## 🔧 已修复的代码

我已经修改了 `callDoubaoAPI` 云函数，现在它会：

1. **优先从 `ai_configs` 读取配置**
2. **如果没有，尝试从 `system_config` 读取**
3. **最后降级到环境变量**

这样可以兼容两套配置系统。

## ✅ 验证修复

修复后，运行以下代码验证：

```javascript
// 测试AI评语生成
wx.cloud.callFunction({
  name: 'callDoubaoAPI',
  data: {
    style: 'warm',
    length: 'medium',
    studentName: '测试学生',
    performanceMaterial: '这是一个测试'
  }
}).then(res => {
  console.log('AI调用结果:', res.result);
  if (res.result.success) {
    console.log('✅ 修复成功！AI可以正常工作了');
  } else {
    console.log('❌ 仍有问题:', res.result.error);
  }
});
```

## 🎯 根本解决方案

为了避免将来再出现这种问题，建议：

1. **统一配置系统**：只使用一套配置（建议用 `ai_configs`）
2. **完善管理后台**：确保管理后台操作的是正确的数据集合
3. **添加配置验证**：在保存配置时验证API密钥的有效性

## 📞 如果还有问题

如果按照上述方案仍然无法解决，请：

1. 提供控制台的完整错误日志
2. 运行调试代码并提供输出结果
3. 确认你的豆包AI密钥是否有效且有足够额度

---

**记住：HTTP 401 错误就是认证失败，99%的情况都是API密钥问题！**
