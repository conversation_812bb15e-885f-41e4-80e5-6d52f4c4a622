/**
 * 快速修复AI配置
 * 统一两套配置系统，确保有有效的API密钥
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🔧 开始快速修复AI配置...');
  
  try {
    const { apiKey, action = 'auto' } = event;
    const result = {
      timestamp: new Date().toISOString(),
      actions: [],
      success: false,
      message: ''
    };

    // 1. 检查当前配置状态
    console.log('📋 检查当前配置状态...');
    
    const aiConfigsResult = await db.collection('ai_configs').get();
    const systemConfigResult = await db.collection('system_config')
      .where({ type: 'ai_config' })
      .get();

    result.actions.push(`检查到 ai_configs: ${aiConfigsResult.data.length} 条记录`);
    result.actions.push(`检查到 system_config: ${systemConfigResult.data.length} 条记录`);

    // 2. 根据action执行不同的修复策略
    if (action === 'auto') {
      // 自动修复：优先使用现有配置，如果没有则创建默认配置
      await autoFix(db, result, apiKey);
    } else if (action === 'create_ai_configs') {
      // 强制在 ai_configs 中创建配置
      await createAIConfigsEntry(db, result, apiKey);
    } else if (action === 'sync_from_system') {
      // 从 system_config 同步到 ai_configs
      await syncFromSystemConfig(db, result);
    }

    console.log('✅ 修复完成:', result);
    return result;

  } catch (error) {
    console.error('❌ 修复失败:', error);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    };
  }
};

/**
 * 自动修复策略
 */
async function autoFix(db, result, apiKey) {
  // 检查 ai_configs 是否有激活配置
  const activeAIConfigs = await db.collection('ai_configs')
    .where({ status: 'active' })
    .get();

  if (activeAIConfigs.data.length > 0) {
    const config = activeAIConfigs.data[0];
    if (config.config?.apiKey) {
      result.success = true;
      result.message = 'ai_configs 已有有效配置，无需修复';
      result.actions.push('发现有效的 ai_configs 配置');
      return;
    }
  }

  // 检查 system_config 是否有配置可以同步
  const activeSystemConfigs = await db.collection('system_config')
    .where({ type: 'ai_config', status: 'active' })
    .get();

  if (activeSystemConfigs.data.length > 0) {
    const systemConfig = activeSystemConfigs.data[0];
    if (systemConfig.apiKey) {
      // 同步到 ai_configs
      await syncConfigToAIConfigs(db, systemConfig, result);
      return;
    }
  }

  // 如果提供了 apiKey，创建新配置
  if (apiKey) {
    await createDefaultAIConfig(db, result, apiKey);
  } else {
    result.success = false;
    result.message = '需要提供 API 密钥来创建配置';
    result.actions.push('未找到有效配置，需要提供 apiKey 参数');
  }
}

/**
 * 从 system_config 同步配置到 ai_configs
 */
async function syncConfigToAIConfigs(db, systemConfig, result) {
  const aiConfigData = {
    name: `${systemConfig.provider || '豆包AI'}（从system_config同步）`,
    provider: systemConfig.provider || 'doubao',
    model: systemConfig.model || 'doubao-pro-4k',
    status: 'active',
    config: {
      apiKey: systemConfig.apiKey,
      baseURL: systemConfig.apiUrl || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      maxTokens: systemConfig.maxTokens || 2000,
      temperature: systemConfig.temperature || 0.7
    },
    inputPrice: 0.001,
    outputPrice: 0.002,
    usage: 0,
    totalCost: 0,
    createTime: db.serverDate(),
    updateTime: db.serverDate(),
    createTimestamp: Date.now(),
    updateTimestamp: Date.now(),
    isDefault: true,
    syncedFrom: 'system_config'
  };

  // 先将现有的 ai_configs 设为非激活
  await db.collection('ai_configs')
    .where({ status: 'active' })
    .update({
      data: { status: 'inactive', updateTime: db.serverDate() }
    });

  // 创建新的 ai_configs 记录
  const addResult = await db.collection('ai_configs').add({
    data: aiConfigData
  });

  result.success = true;
  result.message = '成功从 system_config 同步配置到 ai_configs';
  result.actions.push(`创建了新的 ai_configs 记录: ${addResult._id}`);
  result.actions.push('已将其他 ai_configs 设为非激活状态');
}

/**
 * 创建默认AI配置
 */
async function createDefaultAIConfig(db, result, apiKey) {
  const aiConfigData = {
    name: '豆包AI（快速修复）',
    provider: 'doubao',
    model: 'doubao-pro-4k',
    status: 'active',
    config: {
      apiKey: apiKey,
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      maxTokens: 2000,
      temperature: 0.7
    },
    inputPrice: 0.001,
    outputPrice: 0.002,
    usage: 0,
    totalCost: 0,
    createTime: db.serverDate(),
    updateTime: db.serverDate(),
    createTimestamp: Date.now(),
    updateTimestamp: Date.now(),
    isDefault: true,
    createdBy: 'quickFix'
  };

  // 先将现有的配置设为非激活
  await db.collection('ai_configs')
    .where({ status: 'active' })
    .update({
      data: { status: 'inactive', updateTime: db.serverDate() }
    });

  // 创建新配置
  const addResult = await db.collection('ai_configs').add({
    data: aiConfigData
  });

  result.success = true;
  result.message = '成功创建默认AI配置';
  result.actions.push(`创建了新的 ai_configs 记录: ${addResult._id}`);
  result.actions.push('使用提供的 API 密钥');
}
