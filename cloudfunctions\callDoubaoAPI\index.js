/**
 * 通用AI模型调用云函数
 * 支持豆包AI的Chat Completions格式
 * 用于生成AI评语
 */
const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  timeout: 60000 // 强制设置60秒超时
});

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  // 生产环境：隐藏敏感参数信息

  try {
    const { style = 'warm', length = 'medium', temperature = 0.7, max_tokens = 300, studentName = '', performanceMaterial = '' } = event;

    // 移除旧的prompt处理逻辑，现在直接从数据库获取模板

    // 🎯 核心修改：直接从数据库获取提示词模板

    // 🔧 修复：完整的类型映射，将小程序风格精确映射到数据库类型
    const styleMapping = {
      'warm': 'gentle',          // 温暖亲切 → 温和亲切型 (数据库type: gentle)
      'formal': 'formal',        // 正式规范 → 正式规范型 (数据库type: formal)  
      'encouraging': 'encouraging', // 鼓励激励 → 鼓励激励型 (数据库type: encouraging)
      'detailed': 'detailed'     // 详细具体 → 详细具体型 (数据库type: detailed)
      // 注：数据库还有comprehensive(综合发展型)，但小程序端未使用
    };
    
    const dbType = styleMapping[style] || style;

    let promptTemplate = '';
    
    try {
      const db = cloud.database();
      const templateResult = await db.collection('prompt_templates')
        .where({
          type: dbType,
          enabled: true
        })
        .orderBy('version', 'desc')
        .limit(1)
        .get();
      
      if (templateResult.data && templateResult.data.length > 0) {
        const template = templateResult.data[0];
        promptTemplate = template.content;

        // 🔧 修复：允许空姓名，但记录警告
        if (!studentName || studentName.trim() === '') {

          studentName = ''; // 确保为空字符串而不是undefined
        }

        // 🔧 修复：处理行为记录变量
        
        // 确保行为记录有默认值
        const finalPerformanceMaterial = performanceMaterial && performanceMaterial.trim() !== ''
          ? performanceMaterial
          : '暂无具体表现记录，请根据学生的一般情况生成评语';

        // 替换模板变量
        promptTemplate = promptTemplate
          .replace(/{{学生姓名}}/g, studentName || '该同学')
          .replace(/{{行为记录}}/g, finalPerformanceMaterial);

        // 🔧 修复：全面的变量替换验证逻辑
        // 检查模板中是否还有未替换的变量标记
        const unreplacedNameVars = (promptTemplate.match(/{{学生姓名}}/g) || []).length;
        const unreplacedRecordVars = (promptTemplate.match(/{{行为记录}}/g) || []).length;
        const hasUnreplacedVariables = unreplacedNameVars > 0 || unreplacedRecordVars > 0;

        if (studentName && studentName.trim() !== '') {
          const nameOccurrences = (promptTemplate.match(new RegExp(studentName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || []).length;

        }

        // 🔧 修复：只有在确实存在未替换变量时才报错
        if (hasUnreplacedVariables) {
          console.error('🎯 模板变量替换失败，存在未替换的变量标记');
          console.error('🔍 详细信息:', {
            未替换学生姓名: unreplacedNameVars,
            未替换行为记录: unreplacedRecordVars,
            模板片段: promptTemplate.substring(0, 500)
          });
          return {
            success: false,
            error: '模板变量替换失败',
            message: `提示词模板中存在未替换的变量标记：学生姓名(${unreplacedNameVars})，行为记录(${unreplacedRecordVars})`
          };
        }

        // 记录替换成功信息

        if (!studentName || studentName.trim() === '') {

        }
        
        // 🔧 修复：强化姓名和内容要求
        if (studentName && studentName.trim() !== '') {
          const expectedTitle = studentName + '同学';  // 🔧 修复：使用完整姓名+同学
          promptTemplate += `\n\n【严格要求】：
1. 学生称呼必须是"${expectedTitle}"，严禁使用"${studentName.slice(-1)}同学"或其他任何称呼！
2. 评语内容必须严格基于以下行为记录："${finalPerformanceMaterial}"
3. 严禁编造任何不在行为记录中的内容（如数学成绩、作业情况、班级职务等）
4. 如果行为记录为空或无关，请明确说明"暂无具体表现记录"
5. 评语必须与提供的行为记录高度相关，不得偏离主题`;

        } else {
          promptTemplate += `\n\n【关键要求】：请生成一份通用的学生评语，使用"该同学"作为称呼。`;

        }

      } else {
        throw new Error(`未找到类型为 ${dbType} (原始类型: ${style}) 的提示词模板`);
      }
    } catch (templateError) {
      console.error('🎯 从数据库获取提示词失败:', templateError);
      return {
        success: false,
        error: `获取提示词模板失败: ${templateError.message}`,
        message: '请在管理后台检查提示词模板配置'
      };
    }

    // 🎯 统一架构：支持两套配置系统，优先级：ai_configs > system_config > 环境变量
    let aiConfig = null;

    try {
      const db = cloud.database();

      // 方案1：尝试从 ai_configs 集合获取配置
      console.log('🔍 尝试从 ai_configs 集合获取配置...');
      const aiConfigsResult = await db.collection('ai_configs')
        .where({
          status: 'active'
        })
        .orderBy('updateTime', 'desc')
        .limit(1)
        .get();

      if (aiConfigsResult.data && aiConfigsResult.data.length > 0) {
        const dbConfig = aiConfigsResult.data[0];

        if (dbConfig.config?.apiKey) {
          aiConfig = {
            model: dbConfig.model,
            apiUrl: dbConfig.config?.baseURL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
            apiKey: dbConfig.config?.apiKey,
            provider: `${dbConfig.name}（ai_configs）`,
            maxTokens: dbConfig.config?.maxTokens || 2000,
            temperature: dbConfig.config?.temperature || 0.7
          };
          console.log(`✅ 使用 ai_configs 配置: ${dbConfig.name}`);
        } else {
          console.log('⚠️ ai_configs 配置缺少 API 密钥');
        }
      } else {
        console.log('⚠️ ai_configs 集合无激活配置');
      }

      // 方案2：如果 ai_configs 没有有效配置，尝试 system_config
      if (!aiConfig) {
        console.log('🔍 尝试从 system_config 集合获取配置...');
        const systemConfigResult = await db.collection('system_config')
          .where({
            type: 'ai_config',
            status: 'active'
          })
          .orderBy('updateTime', 'desc')
          .limit(1)
          .get();

        if (systemConfigResult.data && systemConfigResult.data.length > 0) {
          const dbConfig = systemConfigResult.data[0];

          if (dbConfig.apiKey) {
            aiConfig = {
              model: dbConfig.model,
              apiUrl: dbConfig.apiUrl || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
              apiKey: dbConfig.apiKey,
              provider: `${dbConfig.provider || 'AI'}（system_config）`,
              maxTokens: dbConfig.maxTokens || 2000,
              temperature: dbConfig.temperature || 0.7
            };
            console.log(`✅ 使用 system_config 配置: ${dbConfig.provider}`);
          } else {
            console.log('⚠️ system_config 配置缺少 API 密钥');
          }
        } else {
          console.log('⚠️ system_config 集合无激活配置');
        }
      }

      // 方案3：如果数据库都没有配置，降级到环境变量
      if (!aiConfig) {
        console.log('🔍 降级到环境变量配置...');
        aiConfig = {
          model: process.env.DOUBAO_MODEL || 'doubao-seed-1-6-flash-250715',
          apiUrl: process.env.DOUBAO_API_URL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
          apiKey: process.env.DOUBAO_API_KEY,
          provider: '豆包AI（环境变量）',
          maxTokens: 2000,
          temperature: 0.7
        };

        if (aiConfig.apiKey) {
          console.log('✅ 使用环境变量配置');
        } else {
          console.log('❌ 环境变量也没有 API 密钥');
        }
      }

    } catch (dbError) {
      console.error('❌ 从数据库获取配置失败:', dbError);

      // 最后的降级方案：使用环境变量
      aiConfig = {
        model: process.env.DOUBAO_MODEL || 'doubao-seed-1-6-flash-250715',
        apiUrl: process.env.DOUBAO_API_URL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
        apiKey: process.env.DOUBAO_API_KEY,
        provider: '豆包AI（降级配置）',
        maxTokens: 2000,
        temperature: 0.7
      };
    }

    // 验证API密钥是否存在
    if (!aiConfig || !aiConfig.apiKey) {
      console.error('未找到有效的API密钥');
      return {
        success: false,
        error: 'AI配置错误：缺少API密钥',
        message: '请在管理后台的AI模型管理中配置API密钥，或联系管理员设置环境变量',
        details: {
          configSource: aiConfig?.provider || '未知',
          hasConfig: !!aiConfig,
          hasApiKey: !!(aiConfig?.apiKey)
        }
      };
    }

    // 🎯 使用数据库模板调用豆包AI API

    try {
      const response = await Promise.race([
        callAIAPI({
          prompt: promptTemplate, // 🎯 直接使用从数据库获取的完整提示词
          style,
          length,
          temperature: aiConfig.temperature || temperature, // 优先使用配置中的temperature
          max_tokens: aiConfig.maxTokens || max_tokens, // 优先使用配置中的maxTokens
          config: aiConfig
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('豆包AI响应超时')), 50000) // 50秒超时
        )
      ]);
      
      // AI调用成功，进行后处理验证
      let generatedContent = response.content;

      // 🔧 修复：使用完整姓名验证
      const expectedName = studentName + '同学'; // 期望的称呼格式：完整姓名+同学
      const hasCorrectName = generatedContent.includes(expectedName);

      // 🔧 强化内容相关性验证
      const recordKeywords = performanceMaterial ? performanceMaterial.match(/[\u4e00-\u9fa5]+/g) || [] : [];

      // 检查生成内容是否与行为记录相关
      let contentRelevant = false;
      if (recordKeywords.length > 0) {
        contentRelevant = recordKeywords.some(keyword =>
          keyword.length > 1 && generatedContent.includes(keyword)
        );
      }

      // 如果AI使用了错误的姓名或内容不相关，进行修正
      let nameFixed = false;
      let contentFixed = false;

      if (!hasCorrectName) {

        // 常见的错误称呼模式
        const wrongPatterns = [
          /[一-龥]同学/g,  // 任何单字+同学
          /小[一-龥]+同学/g, // 小XX同学
          /该同学/g,        // 该同学
          /这位同学/g       // 这位同学
        ];

        wrongPatterns.forEach(pattern => {
          if (pattern.test(generatedContent)) {
            generatedContent = generatedContent.replace(pattern, expectedName);

            nameFixed = true;
          }
        });

        // 如果开头没有正确称呼，添加
        if (!generatedContent.startsWith(expectedName)) {
          generatedContent = expectedName + '，' + generatedContent;

          nameFixed = true;
        }
      }

      // 🔧 如果内容完全不相关，添加警告
      if (performanceMaterial && !contentRelevant) {

        contentFixed = true;
      }
      
      return {
        success: true,
        data: {
          content: generatedContent
        },
        message: '豆包AI生成成功',
        isFallback: false,
        nameFixed: nameFixed,
        contentRelevant: contentRelevant,
        debugInfo: {
          expectedName: expectedName,
          hasCorrectName: hasCorrectName,
          recordKeywords: recordKeywords,
          contentFixed: contentFixed
        }
      };
      
    } catch (aiError) {
      console.error('豆包AI API调用失败:', aiError);
      
      return {
        success: false,
        error: `豆包AI调用失败: ${aiError.message}`,
        message: '豆包AI服务异常，请稍后重试'
      };
    }

  } catch (error) {
    console.error('云函数执行失败:', error);
    
    // 🎯 不再使用备用方案，直接返回错误
    return {
      success: false,
      error: `云函数执行失败: ${error.message}`,
      message: '评语生成失败，请重试或检查提示词模板配置'
    };
  }
};

/**
 * 🧹 清理和验证AI响应数据
 */
function cleanAndValidateResponse(rawData) {
  console.log('🧹 开始清理响应数据...');

  if (!rawData) {
    throw new Error('响应数据为空');
  }

  let cleanedData = rawData;

  // 移除BOM标记
  if (cleanedData.charCodeAt(0) === 0xFEFF) {
    cleanedData = cleanedData.slice(1);
    console.log('🧹 移除BOM标记');
  }

  // 移除前后空白字符
  cleanedData = cleanedData.trim();

  // 移除可能的控制字符
  cleanedData = cleanedData.replace(/[\x00-\x1F\x7F]/g, '');

  // 检查是否为空
  if (!cleanedData) {
    throw new Error('清理后的响应数据为空');
  }

  // 检查是否看起来像JSON
  if (!cleanedData.startsWith('{') && !cleanedData.startsWith('[')) {
    console.log('⚠️ 响应不以JSON字符开始，尝试查找JSON片段...');

    const jsonStart = cleanedData.indexOf('{');
    const jsonEnd = cleanedData.lastIndexOf('}');

    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      cleanedData = cleanedData.substring(jsonStart, jsonEnd + 1);
      console.log('🧹 提取JSON片段成功');
    } else {
      throw new Error('无法在响应中找到有效的JSON结构');
    }
  }

  console.log('✅ 响应数据清理完成，长度:', cleanedData.length);
  return cleanedData;
}

/**
 * 🔧 健壮的豆包AI响应解析函数
 * 能够处理各种可能的响应格式和异常情况
 */
function parseDoubaoResponse(rawData, statusCode) {
  const debug = {
    originalLength: rawData.length,
    statusCode: statusCode,
    timestamp: new Date().toISOString()
  };

  try {
    console.log('🔧 开始解析豆包AI响应...');
    console.log('📊 原始数据长度:', rawData.length);
    console.log('📊 HTTP状态码:', statusCode);
    console.log('📊 原始数据前200字符:', rawData.substring(0, 200));

    // 1. 检查HTTP状态码
    if (statusCode !== 200) {
      return {
        success: false,
        error: `HTTP状态码错误: ${statusCode}`,
        debug: { ...debug, rawData: rawData.substring(0, 500) }
      };
    }

    // 2. 基础数据检查
    if (!rawData || rawData.length === 0) {
      return {
        success: false,
        error: '响应数据为空',
        debug: debug
      };
    }

    // 3. 数据清理
    let cleanedData = rawData;

    // 移除BOM标记
    if (cleanedData.charCodeAt(0) === 0xFEFF) {
      cleanedData = cleanedData.slice(1);
      debug.bomRemoved = true;
    }

    // 移除前后空白字符
    cleanedData = cleanedData.trim();

    // 移除控制字符
    const beforeControlRemoval = cleanedData.length;
    cleanedData = cleanedData.replace(/[\x00-\x1F\x7F]/g, '');
    if (cleanedData.length !== beforeControlRemoval) {
      debug.controlCharsRemoved = beforeControlRemoval - cleanedData.length;
    }

    debug.cleanedLength = cleanedData.length;

    if (!cleanedData) {
      return {
        success: false,
        error: '清理后数据为空',
        debug: debug
      };
    }

    // 4. JSON提取和解析
    let jsonData = cleanedData;

    // 如果不以JSON字符开始，尝试提取JSON片段
    if (!jsonData.startsWith('{') && !jsonData.startsWith('[')) {
      console.log('⚠️ 数据不以JSON字符开始，尝试提取JSON片段...');

      const jsonStart = jsonData.indexOf('{');
      const jsonEnd = jsonData.lastIndexOf('}');

      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        jsonData = jsonData.substring(jsonStart, jsonEnd + 1);
        debug.jsonExtracted = true;
        debug.extractedLength = jsonData.length;
        console.log('✅ JSON片段提取成功，长度:', jsonData.length);
      } else {
        return {
          success: false,
          error: '无法找到有效的JSON结构',
          debug: { ...debug, searchedData: cleanedData.substring(0, 200) }
        };
      }
    }

    // 5. 解析JSON
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(jsonData);
      debug.parseSuccess = true;
      console.log('✅ JSON解析成功');
    } catch (parseError) {
      return {
        success: false,
        error: `JSON解析失败: ${parseError.message}`,
        debug: {
          ...debug,
          parseError: parseError.message,
          jsonData: jsonData.substring(0, 300)
        }
      };
    }

    // 6. 多种格式的内容提取
    let content = null;
    let usage = null;

    // 格式1: 标准OpenAI格式 {choices: [{message: {content: "..."}}]}
    if (parsedResponse.choices && Array.isArray(parsedResponse.choices) && parsedResponse.choices.length > 0) {
      const choice = parsedResponse.choices[0];
      if (choice.message && typeof choice.message.content === 'string') {
        content = choice.message.content.trim();
        usage = parsedResponse.usage;
        debug.format = 'openai_standard';
        console.log('✅ 使用标准OpenAI格式提取内容');
      }
    }

    // 格式2: 直接内容格式 {content: "..."}
    if (!content && parsedResponse.content && typeof parsedResponse.content === 'string') {
      content = parsedResponse.content.trim();
      debug.format = 'direct_content';
      console.log('✅ 使用直接内容格式提取内容');
    }

    // 格式3: 豆包特殊格式 {result: {content: "..."}}
    if (!content && parsedResponse.result && parsedResponse.result.content && typeof parsedResponse.result.content === 'string') {
      content = parsedResponse.result.content.trim();
      debug.format = 'doubao_result';
      console.log('✅ 使用豆包结果格式提取内容');
    }

    // 格式4: 文本格式 {text: "..."}
    if (!content && parsedResponse.text && typeof parsedResponse.text === 'string') {
      content = parsedResponse.text.trim();
      debug.format = 'text_field';
      console.log('✅ 使用文本字段格式提取内容');
    }

    // 格式5: 如果响应本身就是字符串
    if (!content && typeof parsedResponse === 'string') {
      content = parsedResponse.trim();
      debug.format = 'string_response';
      console.log('✅ 响应本身为字符串格式');
    }

    // 7. 验证提取的内容
    if (!content) {
      return {
        success: false,
        error: '无法从响应中提取有效内容',
        debug: {
          ...debug,
          responseStructure: Object.keys(parsedResponse),
          fullResponse: JSON.stringify(parsedResponse, null, 2).substring(0, 500)
        }
      };
    }

    if (content.length === 0) {
      return {
        success: false,
        error: '提取的内容为空',
        debug: debug
      };
    }

    // 8. 成功返回
    console.log('✅ 内容提取成功，格式:', debug.format, '长度:', content.length);

    return {
      success: true,
      content: content,
      usage: usage,
      debug: debug
    };

  } catch (unexpectedError) {
    return {
      success: false,
      error: `解析过程发生意外错误: ${unexpectedError.message}`,
      debug: {
        ...debug,
        unexpectedError: unexpectedError.message,
        stack: unexpectedError.stack
      }
    };
  }
}

/**
 * 调用AI API (使用内置https，避免axios加载时间)
 */
async function callAIAPI({ prompt, style, length, temperature, max_tokens, config }) {
  const https = require('https');
  const { URL } = require('url');

  // 🎯 构建请求数据 - 直接使用数据库提示词模板

  const requestData = {
    model: config.model,
    messages: [
      {
        role: 'user', 
        content: prompt // 🎯 直接使用从数据库获取的完整提示词模板
      }
    ],
    temperature: temperature,
    max_tokens: max_tokens,
    stream: false
  };

  // AI API调用中

  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(requestData);
    const parsedUrl = new URL(config.apiUrl);
    
    const options = {
      hostname: parsedUrl.hostname,
      port: 443,
      path: parsedUrl.pathname,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          // 🔍 详细记录原始响应数据用于调试
          console.log('🔍 豆包AI原始响应状态码:', res.statusCode);
          console.log('🔍 豆包AI原始响应头:', JSON.stringify(res.headers, null, 2));
          console.log('🔍 豆包AI原始响应数据长度:', data.length);
          console.log('🔍 豆包AI原始响应数据前500字符:', data.substring(0, 500));

          // 检查HTTP状态码
          if (res.statusCode !== 200) {
            console.error('❌ HTTP状态码异常:', res.statusCode);
            console.error('❌ 响应数据:', data);
            reject(new Error(`HTTP ${res.statusCode} 错误`));
            return;
          }

          // 🔧 使用更健壮的响应处理逻辑
          const parseResult = parseDoubaoResponse(data, res.statusCode);

          if (parseResult.success) {
            console.log('✅ 响应解析成功，内容长度:', parseResult.content.length);
            console.log('✅ 内容预览:', parseResult.content.substring(0, 100) + (parseResult.content.length > 100 ? '...' : ''));

            resolve({
              content: parseResult.content,
              usage: parseResult.usage || {
                prompt_tokens: 0,
                completion_tokens: 0,
                total_tokens: 0
              }
            });
          } else {
            console.error('❌ 响应解析失败:', parseResult.error);
            console.error('❌ 调试信息:', parseResult.debug);
            reject(new Error(`响应解析失败: ${parseResult.error}`));
          }

        } catch (unexpectedError) {
          console.error('❌ 响应处理过程中发生意外错误:', unexpectedError);
          console.error('❌ 错误堆栈:', unexpectedError.stack);
          console.error('❌ 原始数据长度:', data.length);
          console.error('❌ 原始数据前1000字符:', data.substring(0, 1000));
          reject(new Error(`响应处理失败: ${unexpectedError.message}`));
        }
      });
    });

    req.on('error', (error) => {
      console.error('HTTPS请求失败:', error);
      reject(error);
    });

    // 不设置超时，等待AI完全生成内容
    // req.setTimeout() 已移除，让AI有充分时间生成内容

    req.write(postData);
    req.end();
  });
}

// 🎯 移除系统提示词函数，完全依赖数据库模板
// 不再需要 getSystemPrompt 函数，因为现在直接使用数据库中的完整提示词模板

// 🎯 移除本地备用评语生成函数
// 现在完全依赖数据库模板 + AI生成，不再使用本地备用评语
// 如果AI调用失败，直接返回错误，让用户重试

