/**
 * 调试AI配置的云函数
 * 检查两套配置系统的状态
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🔍 开始调试AI配置系统...');
  
  try {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      ai_configs: null,
      system_config: null,
      recommendation: ''
    };

    // 1. 检查 ai_configs 集合
    console.log('📋 检查 ai_configs 集合...');
    const aiConfigsResult = await db.collection('ai_configs').get();
    debugInfo.ai_configs = {
      total: aiConfigsResult.data.length,
      active: aiConfigsResult.data.filter(c => c.status === 'active'),
      all: aiConfigsResult.data.map(c => ({
        id: c._id,
        name: c.name,
        status: c.status,
        hasApiKey: !!(c.config?.apiKey),
        apiKeyPreview: c.config?.apiKey ? `${c.config.apiKey.substring(0, 8)}...` : 'null'
      }))
    };

    // 2. 检查 system_config 集合
    console.log('📋 检查 system_config 集合...');
    const systemConfigResult = await db.collection('system_config')
      .where({ type: 'ai_config' })
      .get();
    debugInfo.system_config = {
      total: systemConfigResult.data.length,
      active: systemConfigResult.data.filter(c => c.status === 'active'),
      all: systemConfigResult.data.map(c => ({
        id: c._id,
        type: c.type,
        status: c.status,
        hasApiKey: !!c.apiKey,
        apiKeyPreview: c.apiKey ? `${c.apiKey.substring(0, 8)}...` : 'null'
      }))
    };

    // 3. 分析问题并给出建议
    const activeAiConfigs = debugInfo.ai_configs.active.length;
    const activeSystemConfigs = debugInfo.system_config.active.length;

    if (activeAiConfigs === 0 && activeSystemConfigs === 0) {
      debugInfo.recommendation = '❌ 两个集合都没有激活的配置，需要配置API密钥';
    } else if (activeAiConfigs > 0 && activeSystemConfigs === 0) {
      debugInfo.recommendation = '✅ ai_configs有配置，callDoubaoAPI应该能正常工作';
    } else if (activeAiConfigs === 0 && activeSystemConfigs > 0) {
      debugInfo.recommendation = '⚠️ 只有system_config有配置，但callDoubaoAPI读取ai_configs，需要同步配置';
    } else {
      debugInfo.recommendation = '⚠️ 两个集合都有配置，可能存在冲突，建议统一';
    }

    console.log('🎯 调试结果:', debugInfo);

    return {
      success: true,
      data: debugInfo
    };

  } catch (error) {
    console.error('❌ 调试失败:', error);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    };
  }
};
