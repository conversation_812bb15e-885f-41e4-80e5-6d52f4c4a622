/**
 * 修复AI模型状态云函数
 * 确保只有一个模型处于active状态
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log('🔧 开始修复AI模型状态...');
  
  try {
    // 获取所有活跃的模型
    const activeModelsResult = await db.collection('ai_configs').where({
      status: 'active'
    }).get();

    console.log('📊 当前活跃模型数量:', activeModelsResult.data.length);
    console.log('📋 活跃模型列表:', activeModelsResult.data.map(m => ({
      id: m._id,
      name: m.name,
      provider: m.provider,
      model: m.model,
      hasApiKey: !!(m.config?.apiKey)
    })));

    if (activeModelsResult.data.length <= 1) {
      return {
        success: true,
        message: '模型状态正常，无需修复',
        activeModels: activeModelsResult.data.length,
        currentActiveModel: activeModelsResult.data[0] || null
      };
    }

    // 选择要保持激活的模型（优先选择有API密钥的）
    let keepActiveModel = null;
    
    // 1. 优先选择有API密钥的模型
    const modelsWithApiKey = activeModelsResult.data.filter(m => m.config?.apiKey);
    if (modelsWithApiKey.length > 0) {
      keepActiveModel = modelsWithApiKey[0];
      console.log('🎯 选择有API密钥的模型保持激活:', keepActiveModel.name);
    } else {
      // 2. 如果都没有API密钥，选择第一个
      keepActiveModel = activeModelsResult.data[0];
      console.log('⚠️ 所有模型都没有API密钥，选择第一个保持激活:', keepActiveModel.name);
    }

    // 需要停用的模型
    const modelsToDeactivate = activeModelsResult.data.filter(m => m._id !== keepActiveModel._id);

    console.log('🔧 保持活跃的模型:', keepActiveModel.name);
    console.log('🔧 将要停用的模型:', modelsToDeactivate.map(m => m.name));

    let deactivatedCount = 0;
    const errors = [];
    const deactivatedModels = [];

    // 批量停用多余的活跃模型
    for (const model of modelsToDeactivate) {
      try {
        await db.collection('ai_configs').doc(model._id).update({
          data: {
            status: 'inactive',
            updateTime: db.serverDate(),
            updateTimestamp: Date.now(),
            statusFixedAt: new Date().toISOString(),
            fixedReason: '自动修复：确保只有一个模型处于激活状态'
          }
        });

        console.log('✅ 已停用模型:', model.name);
        deactivatedCount++;
        deactivatedModels.push({
          id: model._id,
          name: model.name,
          provider: model.provider
        });

      } catch (modelError) {
        console.error('❌ 停用模型失败:', model.name, modelError);
        errors.push({
          modelId: model._id,
          modelName: model.name,
          error: modelError.message
        });
      }
    }

    // 验证修复结果
    const finalActiveModelsResult = await db.collection('ai_configs').where({
      status: 'active'
    }).get();

    console.log('✅ 修复完成，当前活跃模型数量:', finalActiveModelsResult.data.length);

    const result = {
      success: true,
      message: '模型状态修复完成',
      summary: {
        originalActiveCount: activeModelsResult.data.length,
        finalActiveCount: finalActiveModelsResult.data.length,
        deactivatedCount: deactivatedCount,
        errorCount: errors.length
      },
      activeModel: {
        id: keepActiveModel._id,
        name: keepActiveModel.name,
        provider: keepActiveModel.provider,
        model: keepActiveModel.model,
        hasApiKey: !!(keepActiveModel.config?.apiKey),
        apiKeyPreview: keepActiveModel.config?.apiKey ? 
          keepActiveModel.config.apiKey.substring(0, 8) + '...' : 'null'
      },
      deactivatedModels: deactivatedModels,
      errors: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString()
    };

    // 如果激活的模型没有API密钥，添加警告
    if (!keepActiveModel.config?.apiKey) {
      result.warning = '当前激活的模型没有API密钥，需要配置后才能正常使用';
      result.action = '请在管理后台为该模型配置有效的API密钥';
    }

    return result;

  } catch (error) {
    console.error('❌ 修复AI模型状态失败:', error);
    return {
      success: false,
      error: error.message,
      message: '修复失败，请查看错误详情'
    };
  }
};
