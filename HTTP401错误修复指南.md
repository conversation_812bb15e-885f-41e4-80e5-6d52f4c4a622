# 🔑 HTTP 401错误修复指南

## 问题描述
你遇到的"❌ AI调用失败: 豆包AI调用失败: HTTP 401 错误"是**认证失败**问题，通常由以下原因引起：

1. **API密钥无效或过期**
2. **API密钥格式错误**
3. **API密钥权限不足**
4. **API密钥配置位置错误**

## 🚀 快速解决方案

### 方法一：使用检查工具（推荐）

1. **部署检查云函数**
   ```bash
   # 右键点击 cloudfunctions/checkAPIKey 文件夹
   # 选择"上传并部署：云端安装依赖"
   ```

2. **运行检查**
   - 在小程序中访问 `pages/test-fix/test-fix` 页面
   - 点击"检查API密钥"按钮
   - 查看详细的检查结果和修复建议

### 方法二：手动检查修复

#### 1. 检查数据库配置
进入云开发控制台 → 数据库 → `ai_configs` 集合：

```javascript
// 查找激活的配置
db.collection('ai_configs').where({
  status: 'active'
}).get()
```

确保：
- ✅ 只有一个配置的 `status` 为 `'active'`
- ✅ 该配置的 `config.apiKey` 不为空
- ✅ API密钥格式正确（通常很长，包含字母数字）

#### 2. 获取有效的豆包AI密钥
1. 访问豆包AI官网或控制台
2. 登录你的账户
3. 找到API密钥管理页面
4. 创建新的API密钥或复制现有密钥
5. 确保密钥有调用权限和足够额度

#### 3. 更新配置
在云开发控制台中更新激活配置的API密钥：

```javascript
// 更新API密钥
db.collection('ai_configs').doc('配置ID').update({
  data: {
    'config.apiKey': '你的新API密钥',
    updateTime: new Date()
  }
})
```

## 🔍 常见问题排查

### 问题1：API密钥格式错误
**症状**：密钥很短或包含空格
**解决**：
- 确保复制完整的API密钥
- 去除前后空格和换行符
- 检查是否误复制了其他内容

### 问题2：API密钥过期
**症状**：之前能用，现在不能用
**解决**：
- 检查豆包AI账户状态
- 重新生成API密钥
- 确认账户余额充足

### 问题3：权限不足
**症状**：HTTP 403错误或权限相关错误
**解决**：
- 确认API密钥有调用权限
- 检查模型ID是否正确
- 联系豆包AI客服确认权限

### 问题4：配置位置错误
**症状**：配置了但还是401错误
**解决**：
- 确认配置在正确的集合（`ai_configs`）
- 确认配置状态为 `active`
- 重新部署云函数

## 🛠️ 验证修复效果

### 1. 使用检查工具
- 点击"检查API密钥"
- 查看所有检查项是否通过
- 特别关注"API密钥有效性测试"

### 2. 测试生成评语
- 在小程序中尝试生成评语
- 应该不再出现401错误
- 能正常生成AI评语内容

### 3. 查看云函数日志
- 在云开发控制台查看 `callDoubaoAPI` 日志
- 确认没有认证相关错误
- 看到成功的API调用记录

## 📋 检查清单

修复完成后，确认以下项目：

- [ ] 数据库中只有一个 `active` 状态的AI配置
- [ ] 该配置有有效的 `config.apiKey`
- [ ] API密钥格式正确（无空格、换行符）
- [ ] API密钥在豆包AI控制台中有效
- [ ] 模型ID配置正确
- [ ] 云函数已重新部署
- [ ] 测试生成评语功能正常

## 🆘 如果问题仍然存在

### 获取详细信息
1. 运行API密钥检查工具
2. 复制完整的检查结果
3. 查看云函数日志的详细错误

### 联系支持
提供以下信息：
- API密钥检查结果
- 云函数错误日志
- 豆包AI账户状态截图
- 数据库配置截图（隐藏敏感信息）

## 🎯 预防措施

1. **定期检查API密钥**
   - 设置密钥过期提醒
   - 定期更新API密钥

2. **监控API使用**
   - 关注API调用额度
   - 监控错误率

3. **备用配置**
   - 准备多个有效的API密钥
   - 设置自动切换机制

完成以上步骤后，你的HTTP 401错误应该得到解决！
